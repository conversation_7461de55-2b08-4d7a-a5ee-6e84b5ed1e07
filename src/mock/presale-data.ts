import { Presale, PresaleRoundState } from '@/class/interface/presale';

/**
 * Mock presale data for development and testing
 * Includes multiple rounds in different states to test various scenarios
 */
export const mockPresaleData: Presale = {
  presaleContractAddress: '0x1234567890123456789012345678901234567890',
  vestedToken: '0x481fe356df88169f5f38203dd7f3c67b7559fda5',
  collectedToken: '0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913',
  listingTimestamp: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
  claimbackPeriod: 7 * 24 * 60 * 60, // 7 days in seconds
  rounds: [
    {
      roundId: 1,
      state: PresaleRoundState.vesting,
      name: 'Seed Round',
      startTimestamp: Date.now() - 90 * 24 * 60 * 60 * 1000, // 90 days ago
      endTimestamp: Date.now() - 60 * 24 * 60 * 60 * 1000, // 60 days ago
      listingTimestamp: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
      refundsEndTimestamp: Date.now() + 37 * 24 * 60 * 60 * 1000, // 37 days from now
      proofsUri: '/proofs/seed/1/',
      whitelistRoot: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
    },
    {
      roundId: 2,
      state: PresaleRoundState.active,
      name: 'Private Round',
      startTimestamp: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30 days ago
      endTimestamp: Date.now() + 15 * 24 * 60 * 60 * 1000, // 15 days from now
      listingTimestamp: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
      refundsEndTimestamp: Date.now() + 37 * 24 * 60 * 60 * 1000, // 37 days from now
      proofsUri: '/proofs/private/2/',
      whitelistRoot: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
    },
    {
      roundId: 3,
      state: PresaleRoundState.pending,
      name: 'Public Round',
      startTimestamp: Date.now() + 20 * 24 * 60 * 60 * 1000, // 20 days from now
      endTimestamp: Date.now() + 50 * 24 * 60 * 60 * 1000, // 50 days from now
      listingTimestamp: Date.now() + 60 * 24 * 60 * 60 * 1000, // 60 days from now
      refundsEndTimestamp: Date.now() + 67 * 24 * 60 * 60 * 1000, // 67 days from now
      proofsUri: '/proofs/public/3/',
      whitelistRoot: '0xfedcba0987654321fedcba0987654321fedcba0987654321fedcba0987654321',
    },
  ],
};
