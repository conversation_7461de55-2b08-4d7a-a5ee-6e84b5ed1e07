import { useState, useEffect } from 'react';
import { Membership, Presale } from '@/class/interface/presale';
import { ProjectConfig } from '@/providers/project-config-provider';
import { mockPresaleData } from './presale-data';
import { mockMemberships } from './membership-data';
import { mockProjectConfig } from './project-config';

/**
 * Environment variable to enable mock data
 * Set VITE_USE_MOCK_DATA=true in your .env file to enable mock data
 */
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true';

/**
 * Hook for using mock data in development
 * Provides a consistent interface for accessing mock data
 */
export const useMockData = () => {
  const [isEnabled, setIsEnabled] = useState(USE_MOCK_DATA);

  // Allow runtime toggling in development
  const toggleMockData = () => {
    if (import.meta.env.DEV) {
      setIsEnabled(!isEnabled);
      console.log(`Mock data ${!isEnabled ? 'enabled' : 'disabled'}`);
    }
  };

  return {
    isEnabled,
    toggleMockData: import.meta.env.DEV ? toggleMockData : undefined,
    presaleData: isEnabled ? mockPresaleData : null,
    memberships: isEnabled ? mockMemberships : [],
    projectConfig: isEnabled ? mockProjectConfig : null,
  };
};

/**
 * Mock presale instance that mimics the real presale class interface
 * Used to replace the real presale instance in development
 */
export class MockPresaleInstance {
  getVersion() {
    return 'v3' as const;
  }

  getPresaleData(): Presale {
    return mockPresaleData;
  }

  async getMemberships(walletAddress: string): Promise<Membership[]> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Return all mock memberships for any wallet address
    console.log(`Mock: Getting memberships for ${walletAddress}`);
    return mockMemberships;
  }

  async setApproval() {
    console.log('Mock: Setting approval');
    await new Promise(resolve => setTimeout(resolve, 1000));
    return '******************************************' as const;
  }

  async transferMembership() {
    console.log('Mock: Transferring membership');
    await new Promise(resolve => setTimeout(resolve, 1000));
    return '******************************************' as const;
  }

  async claimTokens() {
    console.log('Mock: Claiming tokens');
    await new Promise(resolve => setTimeout(resolve, 2000));
    return '******************************************' as const;
  }

  async claimBackTokens() {
    console.log('Mock: Claiming back tokens');
    await new Promise(resolve => setTimeout(resolve, 2000));
    return '******************************************' as const;
  }

  async buyTokens() {
    console.log('Mock: Buying tokens');
    await new Promise(resolve => setTimeout(resolve, 3000));
    return '******************************************' as const;
  }
}

/**
 * Utility function to check if mock data should be used
 */
export const shouldUseMockData = () => USE_MOCK_DATA;

/**
 * Validation function to ensure mock data matches interfaces
 */
const validateMockData = () => {
  try {
    // Validate presale data structure
    if (!mockPresaleData.presaleContractAddress || !mockPresaleData.rounds) {
      throw new Error('Invalid presale data structure');
    }

    // Validate memberships
    mockMemberships.forEach((membership, index) => {
      if (!membership.id || !membership.roundId || !membership.allocation) {
        throw new Error(`Invalid membership at index ${index}`);
      }
    });

    // Validate project config
    if (!mockProjectConfig.name || !mockProjectConfig.vestedToken) {
      throw new Error('Invalid project config structure');
    }

    return true;
  } catch (error) {
    console.error('Mock data validation failed:', error);
    return false;
  }
};

/**
 * Console helper for development
 */
if (import.meta.env.DEV && USE_MOCK_DATA) {
  console.log('🎭 Mock data is enabled for development');

  if (validateMockData()) {
    console.log('✅ Mock data validation passed');
    console.log('Available mock data:', {
      presaleRounds: mockPresaleData.rounds.length,
      memberships: mockMemberships.length,
      projectName: mockProjectConfig.name,
    });
  } else {
    console.error('❌ Mock data validation failed - check console for details');
  }
}
