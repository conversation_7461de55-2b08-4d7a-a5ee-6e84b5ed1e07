import { base } from 'viem/chains';
import { ProjectConfig } from '@/providers/project-config-provider';

/**
 * Mock project configuration for development and testing
 * Based on the Raiinmaker config but with mock data
 */
export const mockProjectConfig: ProjectConfig = {
  walletConnectProjectId: 'mock-project-id-for-development',
  chain: base,
  name: 'MockCoin Development',
  vestedToken: {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    symbol: 'MOCK',
    decimals: 18,
    address: '******************************************',
    logo: 'https://via.placeholder.com/64x64/6366f1/ffffff?text=MOCK',
  },
  collectedToken: {
    symbol: 'USDC',
    decimals: 6,
    address: '******************************************',
  },
  presaleVersion: 'v3',
  presaleAddress: '******************************************',
  company: {
    name: 'MockCoin Labs',
    website: 'https://mockcoin.dev',
    logo: '/logo/mock-logo.png',
  },
  colors: {
    background: '#1a1b23',
    text: '#ffffff',
    accent: '#6366f1',
    header: {
      background: '#1a1b23',
    },
    footer: {
      background: '#111827',
      text: '#ffffff',
    },
  },
};
