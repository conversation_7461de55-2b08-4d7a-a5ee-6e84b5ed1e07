# Mock Data for Development

This directory contains mock data for development and testing purposes. It allows developers to work on the vest portal without requiring blockchain connections or real presale data.

## 🎯 Purpose

- **Development**: Work without blockchain dependencies
- **Testing**: Consistent, predictable data for testing scenarios
- **Demos**: Showcase different states and scenarios
- **Offline Development**: Continue working without network connectivity

## 📁 Structure

```
src/mock/
├── README.md              # This documentation
├── index.ts              # Main exports
├── presale-data.ts       # Mock presale and rounds data
├── membership-data.ts    # Mock membership data
├── project-config.ts     # Mock project configuration
└── use-mock-data.ts      # Hook and utilities for mock data
```

## 🚀 Quick Start

### 1. Enable Mock Data

Create a `.env` file in the project root:

```bash
# Enable mock data
VITE_USE_MOCK_DATA=true
```

### 2. Use in Development

The mock data will automatically be used when enabled. You can also toggle it at runtime in development:

```typescript
import { useMockData } from '@/mock';

function MyComponent() {
  const { isEnabled, toggleMockData } = useMockData();
  
  return (
    <div>
      <p>Mock data: {isEnabled ? 'ON' : 'OFF'}</p>
      {toggleMockData && (
        <button onClick={toggleMockData}>
          Toggle Mock Data
        </button>
      )}
    </div>
  );
}
```

## 📊 Mock Data Scenarios

### Presale Rounds

1. **Seed Round (ID: 1)** - `vesting` state
   - Already completed, tokens are vesting
   - 25% TGE, linear vesting over 240 days

2. **Private Round (ID: 2)** - `active` state
   - Currently active, can buy tokens
   - 20% TGE, 30-day cliff, then linear vesting

3. **Public Round (ID: 3)** - `pending` state
   - Not started yet, future round
   - 15% TGE, 60-day cliff, then linear vesting

### Memberships

- **Seed membership**: Fully allocated, currently vesting
- **Private memberships**: Partially used, can buy more tokens
- **Public membership**: Not started, large allocation

## 🔧 Integration with Providers

The mock data integrates seamlessly with existing providers:

```typescript
// In your provider
import { useMockData, MockPresaleInstance } from '@/mock';

export const PresaleProvider = ({ children }) => {
  const mockData = useMockData();
  
  const presaleInstance = mockData.isEnabled 
    ? new MockPresaleInstance()
    : await PresaleFactory.createInstance(...);
    
  // ... rest of provider logic
};
```

## ⚠️ Important Notes

### Production Safety

- Mock data is **automatically disabled** in production builds
- Environment variable `VITE_USE_MOCK_DATA` must be explicitly set to `'true'`
- Runtime toggling only works in development mode

### Data Consistency

- All mock data follows the exact same TypeScript interfaces as real data
- Timestamps are relative to current time for realistic scenarios
- BigInt values are properly formatted as strings

### Maintenance

- Update mock data when interfaces change
- Keep scenarios realistic and comprehensive
- Test both mock and real data paths

## 🛠️ Customization

### Adding New Scenarios

1. **New Round**: Add to `presale-data.ts`
```typescript
{
  roundId: 4,
  state: PresaleRoundState.active,
  name: 'Strategic Round',
  // ... other properties
}
```

2. **New Membership**: Add to `membership-data.ts`
```typescript
{
  id: 'membership-4-strategic',
  roundId: 4,
  // ... other properties
}
```

### Modifying Existing Data

Edit the respective files and ensure:
- TypeScript interfaces are satisfied
- Timestamps make logical sense
- BigInt values are strings
- IDs are unique

## 🧪 Testing

Mock data is perfect for testing different scenarios:

```typescript
import { mockMemberships, mockPresaleData } from '@/mock';

describe('VestLayout', () => {
  it('should handle vesting round', () => {
    const vestingRound = mockPresaleData.rounds.find(
      r => r.state === PresaleRoundState.vesting
    );
    // Test vesting scenario
  });
});
```

## 🚫 Removal for Production

To completely remove mock data:

1. Delete the `src/mock/` directory
2. Remove any imports from `@/mock`
3. Remove `VITE_USE_MOCK_DATA` from environment files
4. Clean up any mock-related code in providers
5. Remove `MockDataDemo` component and its usage

The application will work normally without any mock data dependencies.

## 🎯 What's Included

This mock data implementation includes:

- **Complete presale data** with 3 rounds (seed, private, public) in different states
- **Realistic membership data** with proper vesting schedules and allocations
- **Project configuration** that matches the existing structure
- **Environment-based toggling** for safe development/production separation
- **Runtime controls** for easy testing in development
- **TypeScript validation** to ensure data integrity
- **Visual indicator** when mock data is active
- **Comprehensive documentation** for easy maintenance

## 🔄 Current Status

✅ Mock data is fully integrated and ready to use
✅ Environment variables configured (.env.development)
✅ Providers updated to support mock data
✅ Visual demo component added
✅ Documentation complete
✅ TypeScript validation in place

To start using mock data immediately, the environment is already configured in `.env.development`.
