import { Membership } from '@/class/interface/presale';

/**
 * Mock membership data for development and testing
 * Includes memberships for different rounds and scenarios
 */
export const mockMemberships: Membership[] = [
  // Seed Round Membership (Round 1) - Vesting
  {
    id: 'membership-1-seed',
    roundId: 1,
    usage: {
      max: '1800000000000000000000', // 1,800 tokens max
      current: '1800000000000000000000', // Fully used
    },
    price: '100000000000000000', // 0.1 ETH per token
    allocation: '1800000000000000000000', // 1,800 tokens allocated
    claimableBackUnit: '0',
    tgeNumerator: 25, // 25% at TGE
    tgeDenominator: 100,
    cliffDuration: 0, // No cliff
    cliffNumerator: 0,
    cliffDenominator: 1,
    vestingPeriodCount: 20736000, // ~240 days in seconds
    vestingPeriodDuration: 1, // 1 second per period (linear vesting)
    tgeStartTimestamp: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30 days ago
    locked: '1350000000000000000000', // 1,350 tokens still locked (75%)
    unlocked: '450000000000000000000', // 450 tokens unlocked (25% TGE)
    nextUnlockTimestamp: Date.now() + 60 * 60 * 1000, // Next unlock in 1 hour
    nextUnlockValue: '1000000000000000000', // ~1 token per hour
    proofs: [
      '0x091bebcfcc56bdd451b978097a5c8a085662f121b06a084f45d54077680c60d2',
      '0xafa35006473e4d6ee2b4539f82b37b8efa52bf3898696cefa999ed1c5d9b51ef',
    ],
  },
  
  // Private Round Membership (Round 2) - Active
  {
    id: 'membership-2-private',
    roundId: 2,
    usage: {
      max: '4500000000000000000000', // 4,500 tokens max
      current: '2250000000000000000000', // Half used
    },
    price: '150000000000000000', // 0.15 ETH per token
    allocation: '4500000000000000000000', // 4,500 tokens allocated
    claimableBackUnit: '337500000000000000000', // Can claim back 337.5 tokens worth
    tgeNumerator: 20, // 20% at TGE
    tgeDenominator: 100,
    cliffDuration: 2592000, // 30 days cliff
    cliffNumerator: 15, // 15% at cliff
    cliffDenominator: 100,
    vestingPeriodCount: 15552000, // ~180 days in seconds
    vestingPeriodDuration: 1,
    tgeStartTimestamp: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
    locked: '4500000000000000000000', // All locked (not vesting yet)
    unlocked: '0', // Nothing unlocked yet
    nextUnlockTimestamp: Date.now() + 30 * 24 * 60 * 60 * 1000, // TGE in 30 days
    nextUnlockValue: '900000000000000000000', // 900 tokens at TGE (20%)
  },
  
  // Another Private Round Membership (Round 2) - Different allocation
  {
    id: 'membership-2-private-large',
    roundId: 2,
    usage: {
      max: '10800000000000000000000', // 10,800 tokens max
      current: '5400000000000000000000', // Half used
    },
    price: '150000000000000000', // 0.15 ETH per token
    allocation: '10800000000000000000000', // 10,800 tokens allocated
    claimableBackUnit: '810000000000000000000', // Can claim back 810 tokens worth
    tgeNumerator: 20, // 20% at TGE
    tgeDenominator: 100,
    cliffDuration: 2592000, // 30 days cliff
    cliffNumerator: 15, // 15% at cliff
    cliffDenominator: 100,
    vestingPeriodCount: 15552000, // ~180 days in seconds
    vestingPeriodDuration: 1,
    tgeStartTimestamp: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
    locked: '10800000000000000000000', // All locked
    unlocked: '0', // Nothing unlocked yet
    nextUnlockTimestamp: Date.now() + 30 * 24 * 60 * 60 * 1000, // TGE in 30 days
    nextUnlockValue: '2160000000000000000000', // 2,160 tokens at TGE (20%)
  },
  
  // Public Round Membership (Round 3) - Pending
  {
    id: 'membership-3-public',
    roundId: 3,
    usage: {
      max: '27000000000000000000000', // 27,000 tokens max
      current: '0', // Not started yet
    },
    price: '200000000000000000', // 0.2 ETH per token
    allocation: '27000000000000000000000', // 27,000 tokens allocated
    claimableBackUnit: '0',
    tgeNumerator: 15, // 15% at TGE
    tgeDenominator: 100,
    cliffDuration: 5184000, // 60 days cliff
    cliffNumerator: 10, // 10% at cliff
    cliffDenominator: 100,
    vestingPeriodCount: 31104000, // ~360 days in seconds
    vestingPeriodDuration: 1,
    tgeStartTimestamp: Date.now() + 60 * 24 * 60 * 60 * 1000, // 60 days from now
    locked: '27000000000000000000000', // All locked
    unlocked: '0', // Nothing unlocked yet
    nextUnlockTimestamp: Date.now() + 60 * 24 * 60 * 60 * 1000, // TGE in 60 days
    nextUnlockValue: '4050000000000000000000', // 4,050 tokens at TGE (15%)
    proofs: [
      '0xfdb56636f94c9e86546edb9c210edf6d50cf79f949e1a21528e3c2ae71f747e8',
      '0x82ccabde72b4aeae9b8ee3530e5a9f44e7651ed0b8a36bbd25240edc4c9df76f',
    ],
  },
];
