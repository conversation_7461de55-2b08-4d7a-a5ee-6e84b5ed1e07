// import { useContext, useMemo } from 'react';
import { useContext } from 'react';
import { useAccount, useConnect, useSwitchChain } from 'wagmi';

// import { lsGet, lsSet } from '@/lib/local-storage';
import { lsSet } from '@/lib/local-storage';
import { MembershipsProvider } from '@/providers/membership-provider';
import { ProjectConfigContext } from '@/providers/project-config-provider';
import { TokensProvider } from '@/providers/tokens-provider';

import { DisconnectWalletIcon } from './icons';
import { Spinner } from './icons/spinner';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import VestLayout from './vest-layout';
import { MockDataDemo } from './mock-data-demo';

export default function VestApp() {
  const { chain } = useContext(ProjectConfigContext);

  const { connectors, connect } = useConnect();
  const { status, chainId } = useAccount();

  const { switchChain, isPending } = useSwitchChain();

  if (status === 'connecting') {
    return <Spinner className="animate-spin h-12 w-12" />;
  }

  if (status === 'connected') {
    if (chainId !== chain.id) {
      return (
        <Card>
          <CardHeader>
            <CardTitle>Switch network</CardTitle>
            <CardDescription>
              Please switch to the <strong>{chain.name}</strong> network
            </CardDescription>
          </CardHeader>

          <CardContent className="h-full space-y-3">
            <Button
              variant={'secondary'}
              disabled={isPending}
              onClick={() =>
                switchChain({
                  chainId: chain.id,
                })
              }
            >
              Switch network
            </Button>
          </CardContent>
        </Card>
      );
    }

    return (
      <TokensProvider>
        <MembershipsProvider>
          <VestLayout />
          <MockDataDemo />
        </MembershipsProvider>
      </TokensProvider>
    );
  }

  // remove duplication
  const _connectors = connectors.filter(
    (connector, index, self) =>
      index === self.findIndex((c) => c.name === connector.name),
  );

  return (
    <div className="flex items-center justify-center min-w-[35%]">
      <Dialog>
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Connect your wallet</CardTitle>
          </CardHeader>

          <CardContent className="h-full space-y-3">
            <DisconnectWalletIcon />

            <DialogTrigger asChild>
              <Button variant={'secondary'}>Connect</Button>
            </DialogTrigger>
          </CardContent>
        </Card>

        <DialogContent className="bg-black border border-white text-white">
          <DialogHeader>
            <DialogTitle>Connect wallet</DialogTitle>
          </DialogHeader>

          <DialogDescription asChild>
            <div className="grid gap-2 auto-rows-fr">
              {_connectors.map((connector) => (
                <Button
                  key={connector.uid}
                  onClick={() =>
                    connect(
                      { connector },
                      {
                        onSuccess: () => {
                          lsSet('wagmiConnected', 'true', 'string');
                        },
                        onError: () => {
                          lsSet('wagmiConnected', '', 'string');
                        },
                      },
                    )
                  }
                  className="flex flex-col gap-2"
                  variant="secondary"
                >
                  {connector.name}
                </Button>
              ))}
            </div>
          </DialogDescription>
        </DialogContent>
      </Dialog>
    </div>
  );
}
