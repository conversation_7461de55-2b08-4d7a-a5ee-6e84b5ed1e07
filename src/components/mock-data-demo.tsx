import { useContext } from 'react';
import { useMockData } from '@/mock';
import { PresaleContext } from '@/providers/presale-provider';
import { MembershipsContext } from '@/providers/membership-provider';

/**
 * Demo component to showcase mock data functionality
 * Only visible in development mode when mock data is available
 */
export const MockDataDemo = () => {
  const mockData = useMockData();
  const { presaleData } = useContext(PresaleContext);
  const { memberships } = useContext(MembershipsContext);

  // Only show in development when mock data is enabled
  if (!import.meta.env.DEV || !mockData.isEnabled) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-purple-900/90 text-white p-4 rounded-lg shadow-lg max-w-sm z-50">
      <div className="flex items-center gap-2 mb-2">
        <span className="text-2xl">🎭</span>
        <h3 className="font-semibold">Mock Data Active</h3>
      </div>
      
      <div className="text-sm space-y-1">
        <p>
          <strong>Rounds:</strong> {presaleData?.rounds.length || 0}
        </p>
        <p>
          <strong>Memberships:</strong> {memberships.length}
        </p>
        <p>
          <strong>Project:</strong> {mockData.projectConfig?.name}
        </p>
      </div>

      {mockData.toggleMockData && (
        <button
          onClick={mockData.toggleMockData}
          className="mt-2 px-3 py-1 bg-purple-700 hover:bg-purple-600 rounded text-xs transition-colors"
        >
          Toggle Mock Data
        </button>
      )}

      <div className="mt-2 text-xs opacity-75">
        Set VITE_USE_MOCK_DATA=false to disable
      </div>
    </div>
  );
};
