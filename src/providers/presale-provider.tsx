import { createContext, useContext, useEffect, useState } from 'react';

// import timemachine from 'timemachine';
import { IPresale, Presale, PresaleRoundState } from '@/class/interface/presale';
import { PresaleFactory } from '@/class/presale-factory';
import { useMockData, MockPresaleInstance } from '@/mock';

import { ProjectConfigContext } from './project-config-provider';
import { PublicClientContext } from './public-client-provider';

interface PresaleContextProps {
  presaleInstance: IPresale | null;
  presaleData: Presale | null;
  selectedRoundId: number;
  handleRoundChange: (roundId: number) => void;
  fetchPresaleData: () => void;
  loading: boolean;
}

export const PresaleContext = createContext<PresaleContextProps>({
  presaleInstance: null,
  presaleData: null,
  selectedRoundId: 0,
  handleRoundChange: () => {},
  fetchPresaleData: () => {},
  loading: true,
});

export const PresaleProvider = ({ children }: { children: React.ReactNode }) => {
  const { presaleAddress, presaleVersion } = useContext(ProjectConfigContext);
  const mockData = useMockData();

  /// --- State
  const { publicClient } = useContext(PublicClientContext);

  const [presaleInstance, setPresaleInstance] = useState<IPresale | null>(null);

  const [presaleData, setPresaleData] = useState<Presale | null>(null);

  const [selectedRoundId, setselectedRoundId] = useState<number>(
    presaleData?.rounds.find((round) => round.state !== PresaleRoundState.vesting)
      ?.roundId ?? 0,
  );

  const [loading, setLoading] = useState(!presaleData);
  /// --- State

  const handleRoundChange = (roundId: number) => {
    setselectedRoundId(roundId);
  };

  const fetchPresale = async () => {
    let _presaleInstance: IPresale;
    let _presaleData: Presale;

    if (mockData.isEnabled && mockData.presaleData) {
      // Use mock data
      console.log('🎭 Using mock presale data');
      _presaleInstance = new MockPresaleInstance();
      _presaleData = mockData.presaleData;
    } else {
      // Use real blockchain data
      _presaleInstance = await PresaleFactory.createInstance(
        presaleVersion,
        publicClient,
        presaleAddress,
      );
      _presaleData = _presaleInstance.getPresaleData();
    }

    setPresaleInstance(_presaleInstance);
    setPresaleData(_presaleData);

    if (selectedRoundId === 0) {
      const activeRound = _presaleData.rounds.find(
        (round) => round.state !== PresaleRoundState.vesting,
      );
      setselectedRoundId(activeRound?.roundId ?? 0);
    }

    // const block = await publicClient.getBlock();

    // console.log({
    //   timestamp: +block.timestamp.toString() * 1000,
    //   date: new Date(+block.timestamp.toString() * 1000),
    // });

    // timemachine.config({
    //   timestamp: +block.timestamp.toString() * 1000,
    //   tick: true,
    // });

    setLoading(false);
  };

  useEffect(() => {
    fetchPresale();
  }, [presaleAddress]);

  return (
    <PresaleContext.Provider
      value={{
        presaleInstance,
        presaleData,
        selectedRoundId,
        handleRoundChange,
        fetchPresaleData: fetchPresale,
        loading,
      }}
    >
      {children}
    </PresaleContext.Provider>
  );
};
